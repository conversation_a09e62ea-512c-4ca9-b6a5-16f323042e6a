services:
  artist-releases-downloader:
    build:
      context: /projects/artist-releases-downloader
      dockerfile: Dockerfile
    container_name: artist-releases-downloader
    ports:
      - 5000:5000
    volumes:
      - /music:/app/music
      # - ./music2:/app/music
      - ./sessions:/app/sessions
      # - ./sessions:/app/sessions
      - ./user_settings:/app/user_settings
      # - ./user_settings:/app/user_settings
      - ./logs:/app/logs
      # - ./logs:/app/logs
      - ./data:/app/data
      # - ./data:/app/data
    environment:
      - SPOTIPY_CLIENT_ID=f5e6e930e8814373b5febc0f76b87d2e
      - SPOTIPY_CLIENT_SECRET=04300cb7ea9440bc90c9beb0d8e91dcf
      - SPOTIPY_REDIRECT_URI=http://*************:5000/callback
      # - SPOTIPY_REDIRECT_URI=http://localhost:5000/callback
      - FLASK_ENV=production
      - FLASK_APP=app.py
      - PRODUCTION=true
      - HOME=/home/<USER>
      # Add SpotDL audio provider
      - SPOTDL_AUDIO_PROVIDER=youtube
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: "0.5"
        reservations:
          memory: 512M
          cpus: "0.25"
    logging:
      driver: json-file
      options:
        max-size: 10m
        max-file: "3"
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:5000/
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    network_mode: bridge
networks: {}
