import spotipy
import dotenv
import os
import json
from spotipy.oauth2 import SpotifyOAuth

dotenv.load_dotenv()

# Need user-follow-read scope to get followed artists
scope = "user-library-read user-follow-read"
client_id = os.getenv("SPOTIFY_CLIENT_ID")
client_secret = os.getenv("SPOTIFY_CLIENT_SECRET")
redirect_uri = "http://localhost:8080/callback"

sp = spotipy.Spotify(
    auth_manager=SpotifyOAuth(
        client_id=client_id,
        client_secret=client_secret,
        redirect_uri=redirect_uri,
        scope=scope,
    )
)


def format_duration(ms):
    seconds = ms // 1000
    minutes = seconds // 60
    seconds = seconds % 60
    return f"{minutes}:{seconds:02d}"


def get_artist_data(artist_id):
    """Get comprehensive artist data including albums and singles"""
    print(f"🌸 Diving into artist {artist_id}'s world... Let's get their info! 🌺")
    try:
        artist = sp.artist(artist_id)
        if not artist:
            print(f"😞 No artist found for {artist_id}. Bummer! 😔")
            return None
        
        artist_info = {
            "id": artist_id,  # Store the ID for duplicate checking
            "name": artist.get("name", ""),
            "genres": artist.get("genres", []),
            "followers": artist.get("followers", {}).get("total", 0),
            "image": (
                artist.get("images", [{}])[0].get("url")
                if artist.get("images")
                else None
            ),
        }
        print(f"🎤 Found {artist_info['name']}! They have {artist_info['followers']} followers. Cool beans! 🫘")

        results = sp.artist_albums(artist_id, album_type="album,single")
        if not results:
            print(f"📭 No releases found for {artist_info['name']}. Empty mailbox! 📬")
            return {
                "artist_info": artist_info,
                "albums": {},
                "singles": {},
            }
        
        releases = results.get("items", [])
        print(f"🎶 Found {len(releases)} initial releases! Let's collect them all... 🎼")
        
        # Handle pagination
        next_url = results.get("next")
        while next_url:
            results = sp.next(results)
            if not results:
                break
            releases.extend(results.get("items", []))
            next_url = results.get("next")
        print(f"📀 Total releases collected: {len(releases)}! Time to sort the goodies... 🍬")

        unique_releases = {}
        for release in releases:
            if release and release.get("name") not in unique_releases:
                unique_releases[release["name"]] = release

        albums = {}
        singles = {}

        for release in unique_releases.values():
            if not release:
                continue
            release_type = release.get("album_type")
            release_name = release.get("name")
            release_date = release.get("release_date")
            image = (
                release.get("images", [{}])[0].get("url")
                if release.get("images")
                else None
            )

            tracks = sp.album_tracks(release["id"])
            if not tracks:
                print(f"🎵 No tracks for {release_name}. Skipping this one... 😴")
                continue
            
            songs = []
            print(f"🎸 Processing tracks for '{release_name}'... Let's groove! 🕺")
            
            # Get all tracks including pagination
            for track in tracks.get("items", []):
                if track:
                    song = {
                        "name": track.get("name", ""),
                        "link": track.get("external_urls", {}).get("spotify", ""),
                        "image": image,
                        "duration": format_duration(track.get("duration_ms", 0)),
                        "track_number": track.get("track_number", 0),
                    }
                    songs.append(song)
            
            # Handle track pagination
            next_tracks_url = tracks.get("next")
            while next_tracks_url:
                tracks = sp.next(tracks)
                if not tracks:
                    break
                for track in tracks.get("items", []):
                    if track:
                        song = {
                            "name": track.get("name", ""),
                            "link": track.get("external_urls", {}).get("spotify", ""),
                            "image": image,
                            "duration": format_duration(track.get("duration_ms", 0)),
                            "track_number": track.get("track_number", 0),
                        }
                        songs.append(song)
                next_tracks_url = tracks.get("next")
            
            print(f"✅ Added {len(songs)} songs from '{release_name}'! 🎉")

            # Sort songs by track number
            songs.sort(key=lambda x: x["track_number"])

            release_data = {"release_date": release_date, "songs": songs}

            if release_type == "album":
                albums[release_name] = release_data
            elif release_type == "single":
                singles[release_name] = release_data

        # Sort albums and singles by release_date descending
        sorted_albums = dict(
            sorted(albums.items(), key=lambda x: x[1]["release_date"], reverse=True)
        )
        sorted_singles = dict(
            sorted(singles.items(), key=lambda x: x[1]["release_date"], reverse=True)
        )

        return {
            "artist_info": artist_info,
            "albums": sorted_albums,
            "singles": sorted_singles,
        }
    except Exception as e:
        print(f"😱 Oops! Error fetching data for artist {artist_id}: {e} 🐛")
        return None


def get_followed_artists():
    """Get all artists that the user follows"""
    print("🎯 Getting all the artists you follow... Let's see who's in your crew! 👥")
    
    followed_artists = []
    results = sp.current_user_followed_artists(limit=50)
    
    if not results or 'artists' not in results:
        print("📭 No followed artists found. Time to follow some artists! 📬")
        return []
    
    artists = results['artists']
    followed_artists.extend(artists.get('items', []))
    
    # Handle pagination
    while artists.get('next'):
        results = sp.next(artists)
        if not results or 'artists' not in results:
            break
        artists = results['artists']
        followed_artists.extend(artists.get('items', []))
    
    print(f"🎉 Found {len(followed_artists)} followed artists! That's a great collection! 🌟")
    return followed_artists


def load_existing_artist_data():
    """Load existing artist data from artist_songs.json"""
    if os.path.exists('artist_songs.json'):
        try:
            with open('artist_songs.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ Error loading existing artist data: {e}")
            return {}
    return {}


def save_artist_data(data):
    """Save artist data to artist_songs.json"""
    try:
        with open('artist_songs.json', 'w') as f:
            json.dump(data, f, indent=4)
        print("💾 Artist data saved successfully! 🌟")
    except Exception as e:
        print(f"❌ Error saving artist data: {e}")


def main():
    print("🚀 Starting the followed artists adventure! Let's add your crew to the collection! 🎵")
    
    # Get followed artists
    followed_artists = get_followed_artists()
    if not followed_artists:
        print("😔 No followed artists to add. Exiting...")
        return
    
    # Load existing artist data
    existing_data = load_existing_artist_data()
    print(f"📚 Loaded existing data for {len(existing_data)} artists")
    
    # Track new additions
    new_artists_added = 0
    skipped_artists = 0
    
    for artist in followed_artists:
        artist_id = artist.get('id')
        artist_name = artist.get('name', 'Unknown Artist')
        
        # Check if artist already exists (by ID or name)
        already_exists = False
        for existing_name, existing_data_item in existing_data.items():
            existing_id = existing_data_item.get('artist_info', {}).get('id')
            if existing_id == artist_id or existing_name == artist_name:
                already_exists = True
                break
        
        if already_exists:
            print(f"⏭️ Skipping {artist_name} - already in collection")
            skipped_artists += 1
            continue
        
        print(f"🎯 Adding new artist: {artist_name}")
        artist_data = get_artist_data(artist_id)
        
        if artist_data:
            existing_data[artist_name] = artist_data
            new_artists_added += 1
            print(f"✅ Successfully added {artist_name}! 🎉")
        else:
            print(f"❌ Failed to get data for {artist_name}")
    
    # Save updated data
    if new_artists_added > 0:
        save_artist_data(existing_data)
        print(f"🎊 Mission accomplished! Added {new_artists_added} new artists to your collection!")
    else:
        print("📝 No new artists were added - all followed artists are already in your collection!")
    
    print(f"📊 Summary: {new_artists_added} added, {skipped_artists} skipped, {len(existing_data)} total artists")
    print("🎉 All done! Your music collection is now up to date! 🚀")


if __name__ == "__main__":
    main()
